<!--车牌识别页面-->
<view class="page-container">
  <!-- 状态栏占位 -->
  <view style="height: {{statusBarHeight}}px;"></view>

  <!-- 顶部标题区域 -->
  <view class="header-section">
    <view class="header-title">车牌识别挪车</view>
    <view class="header-subtitle">拍照识别车牌，快速联系车主</view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content">

    <!-- 拍照上传卡片 -->
    <view class="upload-card">
      <view class="card-header">
        <view class="card-icon">🚗</view>
        <view class="card-title">拍摄车牌照片</view>
      </view>

      <view class="upload-area" wx:if="{{uploadedFiles.length === 0}}">
        <van-uploader
          file-list="{{ uploadedFiles }}"
          bind:after-read="onImageUpload"
          bind:delete="onImageDelete"
          max-count="1"
          accept="image"
          size-type="{{ ['compressed'] }}"
          max-size="{{ 5 * 1024 * 1024 }}"
          upload-text="点击拍照或选择图片"
          upload-icon="photograph"
          preview-image
          preview-full-image
          deletable
        />
        <view class="upload-tips">
          <view class="tip-item">📸 建议正面拍摄，确保车牌清晰</view>
          <view class="tip-item">💡 光线充足，避免反光和阴影</view>
        </view>
      </view>

      <!-- 已上传图片显示 -->
      <view class="uploaded-image" wx:if="{{uploadedFiles.length > 0 && !recognizing}}">
        <van-uploader
          file-list="{{ uploadedFiles }}"
          bind:after-read="onImageUpload"
          bind:delete="onImageDelete"
          max-count="1"
          accept="image"
          preview-image
          preview-full-image
          deletable
        />

        <!-- 识别按钮 -->
        <view class="recognize-action">
          <button class="recognize-btn" bindtap="startRecognition">
            <view class="btn-icon">🔍</view>
            <view class="btn-text">开始识别车牌</view>
          </button>
        </view>
      </view>

      <!-- 识别进度 -->
      <view class="recognition-progress" wx:if="{{recognizing}}">
        <view class="progress-icon">⏳</view>
        <view class="progress-text">正在识别车牌，请稍候...</view>
        <view class="progress-bar">
          <view class="progress-fill"></view>
        </view>
      </view>
    </view>
  
    <!-- 识别结果卡片 -->
    <view class="result-card" wx:if="{{showResult && recognitionResult}}">
      <view class="card-header">
        <view class="card-icon">✅</view>
        <view class="card-title">识别结果</view>
      </view>

      <!-- 车牌号显示 -->
      <view class="plate-display">
        <view class="plate-number">{{recognitionResult.plateNumber}}</view>
        <view class="plate-confidence">识别置信度: {{recognitionResult.confidence}}</view>
      </view>

      <!-- 车主信息 -->
      <view class="owner-info" wx:if="{{ownerInfo}}">
        <view class="owner-header">
          <view class="owner-icon">👤</view>
          <view class="owner-title">车主信息</view>
        </view>

        <view class="info-grid">
          <view class="info-row" wx:if="{{ownerInfo.ownerName}}">
            <view class="info-label">车主姓名</view>
            <view class="info-value">{{ownerInfo.ownerName}}</view>
          </view>

          <view class="info-row phone-row" wx:if="{{ownerInfo.ownerPhone}}" bindtap="makePhoneCall">
            <view class="info-label">联系电话</view>
            <view class="info-value phone-value">{{ownerInfo.ownerPhone}}</view>
            <view class="phone-icon">📞</view>
          </view>

          <view class="info-row" wx:if="{{ownerInfo.houseName}}">
            <view class="info-label">房屋信息</view>
            <view class="info-value">{{ownerInfo.houseName}}</view>
          </view>

          <view class="info-row" wx:if="{{ownerInfo.parkingSpace}}">
            <view class="info-label">车位信息</view>
            <view class="info-value">{{ownerInfo.parkingSpace}}</view>
          </view>
        </view>

        <!-- 联系按钮 -->
        <view class="action-buttons">
          <button class="contact-btn" bindtap="makePhoneCall" wx:if="{{ownerInfo.ownerPhone}}">
            <view class="btn-icon">📞</view>
            <view class="btn-text">联系车主挪车</view>
          </button>
        </view>
      </view>

      <!-- 未找到车主 -->
      <view class="no-owner-info" wx:if="{{!ownerInfo}}">
        <view class="no-owner-icon">❌</view>
        <view class="no-owner-title">未找到车主信息</view>
        <view class="no-owner-desc">该车牌未在系统中登记，请联系物业处理</view>
        <button class="retry-btn" bindtap="retryRecognition">重新识别</button>
      </view>
    </view>

  </view>
</view>
