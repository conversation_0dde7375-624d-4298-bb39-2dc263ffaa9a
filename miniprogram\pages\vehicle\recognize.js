const app = getApp()
import { createFeedbackManager } from '../../utils/feedbackManager.js'
import { handleError, getLoadingManager } from '../../utils/errorHandler.js'

// 创建车牌识别专用的上传管理器
const plateRecognitionManager = createFeedbackManager('plate_recognition')
const loadingManager = getLoadingManager()

Page({
  data: {
    statusBarHeight: 0,
    recognizing: false,
    uploadedFiles: [], // 使用统一的文件列表格式
    recognitionResult: null,
    ownerInfo: null,
    showResult: false,
    businessId: '' // 业务ID
  },

  onLoad() {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync()
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      businessId: plateRecognitionManager.generateBusinessId()
    })

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '车牌识别'
    })
  },

  // van-uploader 上传后回调
  async onImageUpload(e) {
    const { file } = e.detail

    try {
      const newFiles = await plateRecognitionManager.handleFileUpload(
        file,
        this.data.uploadedFiles,
        loadingManager,
        handleError,
        this.data.businessId
      )

      this.setData({
        uploadedFiles: newFiles,
        showResult: false,
        recognitionResult: null,
        ownerInfo: null
      })

      if (newFiles.length > 0) {
        const latestFile = newFiles[newFiles.length - 1]
        if (latestFile.fileId) {
          console.log('文件上传成功，fileId:', latestFile.fileId)
          wx.showToast({
            title: '上传成功',
            icon: 'success'
          })
        }
      }
    } catch (error) {
      console.error('文件上传失败:', error)
      wx.showToast({
        title: '文件上传失败',
        icon: 'none'
      })
    }
  },

  // van-uploader 删除文件回调
  async onImageDelete(e) {
    const { index } = e.detail
    const updatedFiles = await plateRecognitionManager.handleFileDelete(index, this.data.uploadedFiles)
    this.setData({
      uploadedFiles: updatedFiles,
      showResult: false,
      recognitionResult: null,
      ownerInfo: null
    })
  },

  // 手动点击识别按钮
  startRecognition() {
    if (this.data.uploadedFiles.length === 0) {
      wx.showToast({
        title: '请先上传图片',
        icon: 'none'
      })
      return
    }

    const latestFile = this.data.uploadedFiles[this.data.uploadedFiles.length - 1]
    if (latestFile.fileId) {
      this.recognizePlate(latestFile.fileId)
    } else {
      wx.showToast({
        title: '文件上传未完成',
        icon: 'none'
      })
    }
  },

  // 识别车牌
  async recognizePlate(fileId) {
    const that = this

    this.setData({
      recognizing: true,
      showResult: false,
      recognitionResult: null,
      ownerInfo: null
    })

    wx.showLoading({
      title: '识别中...'
    })

    try {
      // 调用识别接口
      const recognitionResult = await this.callRecognitionAPI(fileId)
      console.log('识别结果:', recognitionResult)

      if (recognitionResult.code === 0) {
        that.setData({
          recognitionResult: recognitionResult.data,
          ownerInfo: recognitionResult.data.ownerInfo,
          showResult: true
        })

        if (recognitionResult.data.ownerInfo) {
          wx.showToast({
            title: '识别成功',
            icon: 'success'
          })
        } else {
          wx.showToast({
            title: '未找到车主信息',
            icon: 'none'
          })
        }
      } else {
        wx.showToast({
          title: recognitionResult.msg || '识别失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('车牌识别失败:', error)
      wx.showToast({
        title: error.message || '识别失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
      that.setData({
        recognizing: false
      })
    }
  },

  // 调用识别API
  callRecognitionAPI(fileId) {
    return new Promise((resolve, reject) => {
      app.request({
        url: '/api/wx/vehicle/recognizePlate',
        method: 'POST',
        data: {
          fileId: fileId
        }
      }).then(resolve).catch(reject)
    })
  },

  // 拨打电话
  makePhoneCall() {
    if (!this.data.ownerInfo || !this.data.ownerInfo.ownerPhone) {
      wx.showToast({
        title: '暂无联系电话',
        icon: 'none'
      })
      return
    }

    wx.makePhoneCall({
      phoneNumber: this.data.ownerInfo.ownerPhone,
      success() {
        console.log('拨打电话成功')
      },
      fail(err) {
        console.error('拨打电话失败:', err)
        wx.showToast({
          title: '拨打电话失败',
          icon: 'none'
        })
      }
    })
  },

  // 重新识别
  retryRecognition() {
    this.setData({
      uploadedFiles: [],
      recognitionResult: null,
      ownerInfo: null,
      showResult: false
    })
  }
})
